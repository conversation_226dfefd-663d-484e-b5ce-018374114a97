services:
  app:
    container_name: hivechat
    image: macroverse-docker.pkg.coding.net/caixuan-main/master/hivechat:0.0.5
    ports:
      - "${HOST_PORT}:3000"
    depends_on:
      - db
    environment:
      DATABASE_URL: "************************************/hivechat"
      AUTH_SECRET: ${AUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
      AUTH_TRUST_HOST: ${AUTH_TRUST_HOST}
      AUTH_GOOGLE_ID: ${AUTH_GOOGLE_ID}
      AUTH_GOOGLE_SECRET: ${AUTH_GOOGLE_SECRET}
      AUTH_GITHUB_ID: ${AUTH_GITHUB_ID}
      AUTH_GITHUB_SECRET: ${AUTH_GITHUB_SECRET}
      NEXT_PUBLIC_AMPLITUDE_API_KEY: ${NEXT_PUBLIC_AMPLITUDE_API_KEY}
    restart: unless-stopped
    networks:
      - my-network

  db:
    container_name: hivechat-db
    image: postgres:16.8-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: hivechat
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker:/docker-entrypoint-initdb.d/
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres -d hivechat" ]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - my-network

volumes:
  postgres_data:


networks:
  my-network:
    driver: bridge
