"use client";
import { useState, useCallback } from "react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from 'next/link';
import { Button, Alert, Typography } from 'antd';
import { GithubOutlined } from '@ant-design/icons';
import GoogleLogo from "@/app/images/loginProvider/google.svg";
import logo from "@/app/images/logo.png";
import Hivechat from "@/app/images/hivechat.svg";
import { extractAndValidateCallbackUrl } from '@/app/utils/callbackUrl';
import { getOptimalRedirectUrl } from '@/app/utils/redirectOptimization';
import { trackLogin } from '@/lib/amplitude';

const { Title, Text } = Typography;

// 登录状态枚举
type LoginState = 'idle' | 'authenticating' | 'redirecting';

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 优化状态管理：使用更精细的状态控制
  const [githubState, setGithubState] = useState<LoginState>('idle');
  const [googleState, setGoogleState] = useState<LoginState>('idle');
  const [error, setError] = useState("");

  // 提取并验证 callbackUrl
  const callbackUrl = extractAndValidateCallbackUrl(searchParams);

  // 统一的loading状态，避免重复渲染
  const isAnyLoading = githubState !== 'idle' || googleState !== 'idle';

  const handleGithubLogin = useCallback(async () => {
    // 防止重复点击
    if (isAnyLoading) {
      return;
    }

    // 设置认证状态，清除错误信息
    setGithubState('authenticating');
    setError("");

    try {
      const response = await signIn("github", {
        redirect: false,
        callbackUrl: callbackUrl,
      });

      if (response?.error) {
        console.log("Github login error:", response.error);
        // 认证失败，重置状态
        setError("Github 登录失败，请重试");
        setGithubState('idle');
      } else {
        // 登录成功，立即设置为重定向状态，避免状态闪动
        setGithubState('redirecting');

        // 追踪登录事件
        trackLogin('github');

        // 获取最优重定向URL
        const optimalUrl = await getOptimalRedirectUrl(response?.url || callbackUrl);

        // 直接跳转，不使用setTimeout避免不必要的延迟
        router.push(optimalUrl);
      }
    } catch (error) {
      console.error("Github login error:", error);
      setError("Github 登录失败，请重试");
      setGithubState('idle');
    }
  }, [isAnyLoading, callbackUrl, router]);

  const handleGoogleLogin = useCallback(async () => {
    // 防止重复点击
    if (isAnyLoading) {
      return;
    }

    // 设置认证状态，清除错误信息
    setGoogleState('authenticating');
    setError("");

    try {
      const response = await signIn("google", {
        redirect: false,
        callbackUrl: callbackUrl,
      });

      if (response?.error) {
        console.log("Google login error:", response.error);
        // 认证失败，重置状态
        setError("Google 登录失败，请重试");
        setGoogleState('idle');
      } else {
        // 登录成功，立即设置为重定向状态，避免状态闪动
        setGoogleState('redirecting');

        // 追踪登录事件
        trackLogin('google');

        // 获取最优重定向URL
        const optimalUrl = await getOptimalRedirectUrl(response?.url || callbackUrl);

        // 直接跳转，不使用setTimeout避免不必要的延迟
        router.push(optimalUrl);
      }
    } catch (error) {
      console.error("Google login error:", error);
      setError("Google 登录失败，请重试");
      setGoogleState('idle');
    }
  }, [isAnyLoading, callbackUrl, router]);

  return (
    <div className="min-h-dvh login-page-bg flex">
      {/* 左侧内容区域 */}
      <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-20 xl:px-24 ">
        <div className="mx-auto max-w-xl -mt-20">
          {/* Logo */}
          <div className="flex items-center mb-8">
            <Image src={logo} alt="HiveChat logo" width={40} height={40} />
            <Hivechat className="ml-3" alt="HiveChat text" width={180} height={45} />
          </div>

          {/* 主标题 */}
          <h3 className="text-3xl font-bold leading-10 !text-gray-800 mb-6">
            为团队准备的一站式 AI 助手<br />灵活、便捷、免费
          </h3>

          {/* 副标题 */}
          <Text className="text-lg text-gray-600 leading-relaxed">
            支持顶尖人工智能模型、包括 OpenAI、Claude、Gemini、DeepSeek。
          </Text>
        </div>
      </div>

      {/* 右侧登录表单区域 */}
      <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none w-1/2 lg:px-24 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          {/* 移动端Logo */}
          <div className="flex items-center justify-center mb-8 lg:hidden">
            <Image src={logo} alt="HiveChat logo" width={32} height={32} />
            <Hivechat className="ml-2" alt="HiveChat text" width={156} height={39} />
          </div>

          {/* 登录表单 */}
          <div className="bg-white py-8 px-6 login-form-shadow login-form-container rounded-lg sm:px-10">
            <div className="mb-6">
              <Title level={3} className="!text-2xl !font-semibold !text-gray-900 !mb-2 text-center">
                登录
              </Title>
            </div>

            <div className="space-y-4">
              {/* Google 登录按钮 */}
              <Button
                type="default"
                block
                size="large"
                loading={googleState === 'authenticating'}
                disabled={isAnyLoading}
                onClick={handleGoogleLogin}
                icon={googleState === 'idle' ? <GoogleLogo style={{ width: 18, height: 18 }} /> : undefined}
                className="border-gray-300 hover:border-gray-400 login-button-hover transition-all duration-200"
              >
                {googleState === 'authenticating'
                  ? "正在登录..."
                  : googleState === 'redirecting'
                    ? "正在跳转..."
                    : "使用 Google 登录"
                }
              </Button>

              {/* Github 登录按钮 */}
              <Button
                type="default"
                block
                size="large"
                loading={githubState === 'authenticating'}
                disabled={isAnyLoading}
                onClick={handleGithubLogin}
                icon={githubState === 'idle' ? <GithubOutlined style={{ fontSize: 18 }} /> : undefined}
                className="border-gray-300 hover:border-gray-400 login-button-hover transition-all duration-200"
              >
                {githubState === 'authenticating'
                  ? "正在登录..."
                  : githubState === 'redirecting'
                    ? "正在跳转..."
                    : "使用 Github 登录"
                }
              </Button>
            </div>

            {/* 错误信息 */}
            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                className="mt-4 transition-all duration-300 ease-in-out"
              />
            )}

            {/* 服务条款 */}
            <div className="mt-8 text-center">
              <span className="text-xs text-gray-400">
                登录代表同意我们的
                <Link href="/terms" className="text-blue-600 hover:text-blue-500">
                  服务条款
                </Link>
                和
                <Link href="/privacy" className="text-blue-600 hover:text-blue-500">
                  隐私政策
                </Link>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}